#!/usr/bin/env python3
"""
デバッグ用スクリプト：ファイル更新プロセスの詳細確認
"""

import sys
import json
import hashlib
from pathlib import Path

# パスを追加
sys.path.insert(0, str(Path(__file__).parent))

from indexer.index_updater import CodeIndexer

def debug_update(target_file: str, index_path: str = "./faissdb/faiss.index"):
    """ファイル更新プロセスをデバッグ"""
    
    target_path = Path(target_file).resolve()
    state_file = Path(index_path).with_suffix(".json")
    
    print(f"🔍 ファイル更新デバッグ")
    print("=" * 60)
    print(f"📁 対象ファイル: {target_path}")
    print(f"💾 インデックス状態ファイル: {state_file}")
    print()
    
    # ファイルの存在確認
    if not target_path.exists():
        print("❌ 対象ファイルが見つかりません")
        return
    
    # 現在のファイルハッシュを計算（file_scannerと同じ方法）
    try:
        hasher = hashlib.sha256()
        with target_path.open("rb") as fp:
            for chunk in iter(lambda: fp.read(8192), b""):
                hasher.update(chunk)
        current_hash = hasher.hexdigest()

        # ファイルサイズも取得
        file_size = target_path.stat().st_size
        print(f"📊 現在のファイルハッシュ: {current_hash}")
        print(f"📊 ファイルサイズ: {file_size} バイト")
        print()
    except Exception as e:
        print(f"❌ ファイル読み込みエラー: {e}")
        return
    
    # 状態ファイルを読み込み（更新前）
    if state_file.exists():
        try:
            state = json.loads(state_file.read_text("utf-8"))
            stored_hashes = state.get("hashes", {})
            id_map = state.get("id_map", {})
            
            target_file_str = str(target_path)
            if target_file_str in stored_hashes:
                old_hash = stored_hashes[target_file_str]
                print(f"📊 更新前のハッシュ: {old_hash}")
                print(f"📊 ハッシュ一致: {current_hash == old_hash}")
                
                if target_file_str in id_map:
                    old_chunks = id_map[target_file_str]
                    print(f"📊 更新前のチャンク数: {len(old_chunks)}")
                    print(f"📊 更新前のチャンクID: {old_chunks[:3]}...")
                print()
            else:
                print("⚠️  ファイルが状態に見つかりません")
                print()
                
        except Exception as e:
            print(f"❌ 状態ファイル読み込みエラー: {e}")
            return
    
    # プロジェクトルートを推定
    project_root = target_path.parent
    while project_root.parent != project_root:
        if (project_root / ".git").exists() or (project_root / "pom.xml").exists():
            break
        project_root = project_root.parent
    
    print(f"📁 推定プロジェクトルート: {project_root}")
    
    try:
        # CodeIndexerを初期化
        ci = CodeIndexer(project_root, index_path)
        print("📊 CodeIndexer初期化完了")
        
        # 更新前の統計
        stats_before = ci.get_index_stats()
        print(f"📈 更新前の統計:")
        print(f"  - 総ベクトル数: {stats_before['total_vectors']}")
        print(f"  - 総ファイル数: {stats_before['total_files']}")
        print(f"  - 総チャンク数: {stats_before['total_chunks']}")
        print()
        
        # ファイルを手動で更新
        print("🔄 ファイル更新実行中...")
        ci._handle_change(str(target_path))
        print("✅ ファイル更新完了")
        print()
        
        # 更新後の統計
        stats_after = ci.get_index_stats()
        print(f"📈 更新後の統計:")
        print(f"  - 総ベクトル数: {stats_after['total_vectors']}")
        print(f"  - 総ファイル数: {stats_after['total_files']}")
        print(f"  - 総チャンク数: {stats_after['total_chunks']}")
        print()
        
        # 変更の確認
        vector_diff = stats_after['total_vectors'] - stats_before['total_vectors']
        chunk_diff = stats_after['total_chunks'] - stats_before['total_chunks']
        print(f"📊 変更量:")
        print(f"  - ベクトル数変化: {vector_diff:+d}")
        print(f"  - チャンク数変化: {chunk_diff:+d}")
        print()
        
    except Exception as e:
        print(f"❌ 更新エラー: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 状態ファイルを再読み込み（更新後）
    if state_file.exists():
        try:
            state = json.loads(state_file.read_text("utf-8"))
            stored_hashes = state.get("hashes", {})
            id_map = state.get("id_map", {})
            
            target_file_str = str(target_path)
            if target_file_str in stored_hashes:
                new_hash = stored_hashes[target_file_str]
                print(f"📊 更新後のハッシュ: {new_hash}")
                print(f"📊 ハッシュ一致: {current_hash == new_hash}")
                
                if target_file_str in id_map:
                    new_chunks = id_map[target_file_str]
                    print(f"📊 更新後のチャンク数: {len(new_chunks)}")
                    print(f"📊 更新後のチャンクID: {new_chunks[:3]}...")
                
                if current_hash == new_hash:
                    print("✅ ファイル変更がデータベースに正常に反映されました！")
                else:
                    print("❌ ファイル変更がデータベースに反映されていません")
                    
            else:
                print("❌ 更新後もファイルが状態に見つかりません")
                
        except Exception as e:
            print(f"❌ 更新後の状態ファイル読み込みエラー: {e}")

def main():
    """メイン関数"""
    if len(sys.argv) < 2:
        print("使用方法: python debug_update.py <ファイルパス> [インデックスパス]")
        print("例: python debug_update.py /mnt/d/workspase/ai_pj_demo/pj_demo/pom.xml")
        return
    
    target_file = sys.argv[1]
    index_path = sys.argv[2] if len(sys.argv) > 2 else "./faissdb/faiss.index"
    
    debug_update(target_file, index_path)

if __name__ == "__main__":
    main()
