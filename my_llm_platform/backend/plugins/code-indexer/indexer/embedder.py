# ==============================================================
"""埋め込みベクトル生成ラッパー。"""

from __future__ import annotations
from typing import List

import numpy as np
from sentence_transformers import SentenceTransformer  # type: ignore[import]

DEFAULT_MODEL_NAME = "thenlper/gte-base"


class Embedder:
    """SentenceTransformer ベース埋め込みクラス。"""

    def __init__(self, model_name: str = DEFAULT_MODEL_NAME):
        self.model = SentenceTransformer(model_name)

    def embed(self, chunks: List[str]):
        """文字列リスト → numpy.ndarray(float32) を返す。"""
        return np.array(
            self.model.encode(chunks, normalize_embeddings=True, batch_size=32),
            dtype="float32",
        )


