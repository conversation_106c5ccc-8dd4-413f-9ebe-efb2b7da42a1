# ==============================================================
"""Merkle 木ユーティリティ。

- 葉ノードにはファイルハッシュ (SHA-256) を与える。
- 奇数ノードが発生した場合は最後のノードを複製して補完。
"""

from __future__ import annotations
import hashlib
from typing import List


def build_merkle(leaves: List[str]) -> str:
    """葉ノード配列から Merkle ルートハッシュを計算する。"""
    if not leaves:
        return ""
    # 各葉を SHA-256 化
    current = [hashlib.sha256(l.encode()).hexdigest() for l in leaves]
    # 上位へ畳み込み
    while len(current) > 1:
        if len(current) % 2 == 1:
            current.append(current[-1])  # 奇数補完
        current = [
            hashlib.sha256((current[i] + current[i + 1]).encode()).hexdigest()
            for i in range(0, len(current), 2)
        ]
    return current[0]


