# ==============================================================
"""コードファイルをチャンクに分割するモジュール。

既存 rag_service.DocumentLoader があればそれを優先利用し、
存在しない場合はフォールバック実装を使用する。"""

from __future__ import annotations
from typing import List

try:
    # ユーザ提供の実装
    from ...rag.rag_service import DocumentLoader  # type: ignore

    class CodeChunker:
        """DocumentLoader ラッパー。"""

        def split(self, text: str, path: str) -> List[str]:
            loader = DocumentLoader()
            # DocumentLoader 側に path を渡す API がない場合は text のみ
            return loader.split(text)

except ImportError:

    # フォールバック実装
    class CodeChunker:  # type: ignore[override]
        """シンプルな固定長チャンク化 (500 文字) 実装。"""

        def __init__(self, chunk_size: int = 500):
            self.chunk_size = chunk_size

        def split(self, text: str, _path: str | None = None) -> List[str]:
            return [
                text[i : i + self.chunk_size]  # noqa: E203 : slice interior space
                for i in range(0, len(text), self.chunk_size)
            ]


