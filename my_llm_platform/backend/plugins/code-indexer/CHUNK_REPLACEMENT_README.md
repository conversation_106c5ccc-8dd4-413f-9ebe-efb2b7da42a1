# チャンク置換機能 (Chunk Replacement Functionality)

## 概要

コードインデクサーにチャンク置換機能を実装しました。この機能により、ファイルが変更された際に古いチャンクベクトルを削除し、新しいベクトルのみをインデックスに保持することで、検索精度の向上とインデックスサイズの最適化を実現します。

## 主要機能

### 1. 決定的チャンクID生成
- ファイルパス + チャンク番号のSHA256ハッシュから64bit整数IDを生成
- 同じファイルの同じチャンクは常に同じIDを持つ
- ファイル変更時の古いチャンク特定が可能

### 2. IndexIDMap2による削除機能
- Faissの`IndexIDMap2`でベースインデックスをラップ
- `remove_ids()`メソッドによる効率的なベクトル削除
- 既存のIndexFlatL2からの自動マイグレーション

### 3. 状態管理の強化
- `id_map`: ファイルごとのチャンクID追跡 `{filepath: [id1, id2, ...]}`
- `deleted_count`: 削除されたベクトル数の追跡
- 永続化による状態の保持

### 4. 閾値ベース再構築
- 削除ベクトル数が100,000を超えた場合に再構築を推奨
- `rebuild_index()`メソッドによる完全再構築
- IVF圧縮効率の改善

## API使用例

### 基本的な使用方法

```python
from backend.plugins.code_indexer.indexer.index_updater import CodeIndexer

# インデクサーを初期化
indexer = CodeIndexer("/path/to/repo", "/path/to/index.faiss")

# 初期インデックス構築
indexer.full_build()

# 統計情報を確認
stats = indexer.get_index_stats()
print(f"ファイル数: {stats['total_files']}")
print(f"チャンク数: {stats['total_chunks']}")
print(f"削除数: {stats['deleted_count']}")
print(f"再構築推奨: {stats['rebuild_recommended']}")
```

### ファイル変更の監視

```python
# ファイル変更を自動監視
indexer.watch()

# または手動でファイル変更を処理
indexer._handle_change("/path/to/changed/file.py")
```

### インデックス再構築

```python
# 削除されたベクトルが多い場合の完全再構築
if indexer.get_index_stats()['rebuild_recommended']:
    indexer.rebuild_index()
```

## 内部実装詳細

### チャンクID生成アルゴリズム

```python
def _generate_chunk_id(self, file_path: str, chunk_no: int) -> int:
    content = f"{file_path}#{chunk_no}"
    hash_bytes = hashlib.sha256(content.encode('utf-8')).digest()
    chunk_id = int.from_bytes(hash_bytes[:8], byteorder='big', signed=False)
    return chunk_id % (2**63 - 1)
```

### ファイル変更処理フロー

1. **ファイル変更検出**: ハッシュ値比較による変更検出
2. **古いチャンク削除**: `id_map`から古いIDを取得し`remove_ids()`で削除
3. **新しいチャンク追加**: 新しいIDを生成し`add_with_ids()`で追加
4. **状態更新**: `id_map`と`deleted_count`を更新

### 状態ファイル構造

```json
{
  "hashes": {
    "/path/to/file.py": "sha256_hash"
  },
  "merkle_root": "merkle_tree_root",
  "id_map": {
    "/path/to/file.py": [1234567890, 9876543210]
  },
  "deleted_count": 42
}
```

## パフォーマンス特性

### メリット
- **検索精度向上**: 古いチャンクによる誤検索を防止
- **インデックスサイズ最適化**: 不要なベクトルの除去
- **一貫性保証**: 決定的IDによる確実な置換

### 考慮事項
- **削除オーバーヘッド**: 大量削除時のパフォーマンス影響
- **再構築タイミング**: 閾値到達時の再構築推奨
- **メモリ使用量**: IndexIDMap2による若干の増加

## トラブルシューティング

### よくある問題

1. **インデックスが大きくなりすぎる**
   - `get_index_stats()`で削除数を確認
   - 必要に応じて`rebuild_index()`を実行

2. **検索結果に古い内容が含まれる**
   - ファイル変更が正しく検出されているか確認
   - `id_map`の状態を確認

3. **パフォーマンスが低下する**
   - 削除数が閾値を超えていないか確認
   - インデックス再構築を検討

### デバッグ用コマンド

```python
# 統計情報の詳細確認
stats = indexer.get_index_stats()
print(json.dumps(stats, indent=2))

# 特定ファイルのチャンクID確認
file_path = "/path/to/file.py"
if file_path in indexer._state["id_map"]:
    chunk_ids = indexer._state["id_map"][file_path]
    print(f"File: {file_path}")
    print(f"Chunk IDs: {chunk_ids}")
```

## 今後の改善案

1. **バッチ削除最適化**: 複数ファイル変更時の効率化
2. **圧縮インデックス対応**: IVFインデックスでの削除機能
3. **増分バックアップ**: 状態変更の差分保存
4. **メトリクス収集**: 詳細なパフォーマンス指標

## 関連ファイル

- `index_updater.py`: メイン実装
- `test_standalone.py`: 機能テスト
- `CHUNK_REPLACEMENT_README.md`: このドキュメント
