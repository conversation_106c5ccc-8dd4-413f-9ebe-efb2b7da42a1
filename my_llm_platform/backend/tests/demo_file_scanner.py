#!/usr/bin/env python3
"""DirectoryScanner の使用例とデモ。"""

import sys
from pathlib import Path

# プロジェクトのパスを追加
sys.path.append('backend/plugins/code-indexer')

from ..plugins.code_indexer.file_scanner import DirectoryScanner


def demo_current_project():
    """現在のプロジェクトディレクトリをスキャンするデモ。"""
    print("=== 現在のプロジェクトディレクトリのスキャン ===")
    
    # 現在のディレクトリをスキャン
    scanner = DirectoryScanner(".")
    files = scanner.scan()
    
    print(f"スキャンされたファイル数: {len(files)}")
    print("\nファイル一覧（最初の20個）:")
    
    # ファイルパスをソートして表示
    sorted_files = sorted(files.keys())
    for i, file_path in enumerate(sorted_files[:20]):
        rel_path = Path(file_path).relative_to(Path(".").resolve())
        print(f"  {i+1:2d}. {rel_path}")
    
    if len(sorted_files) > 20:
        print(f"  ... および他 {len(sorted_files) - 20} 個のファイル")
    
    # 除外されたディレクトリの例を表示
    print("\n=== 除外される可能性のあるディレクトリ ===")
    excluded_dirs = [
        "__pycache__", "node_modules", "target", ".git", 
        "venv", ".venv", "dist", "build", ".pytest_cache"
    ]
    
    for dir_name in excluded_dirs:
        if Path(dir_name).exists():
            print(f"  ✓ {dir_name}/ - 存在し、.gitignore で除外される可能性があります")
        else:
            print(f"  - {dir_name}/ - 存在しません")


def demo_specific_directory(target_dir: str):
    """特定のディレクトリをスキャンするデモ。"""
    print(f"\n=== {target_dir} ディレクトリのスキャン ===")
    
    if not Path(target_dir).exists():
        print(f"ディレクトリ {target_dir} が存在しません。")
        return
    
    scanner = DirectoryScanner(target_dir)
    files = scanner.scan()
    
    print(f"スキャンされたファイル数: {len(files)}")
    
    if files:
        print("\nファイル一覧:")
        for file_path in sorted(files.keys())[:10]:  # 最初の10個のみ表示
            rel_path = Path(file_path).relative_to(Path(target_dir).resolve())
            print(f"  {rel_path}")
        
        if len(files) > 10:
            print(f"  ... および他 {len(files) - 10} 個のファイル")


def main():
    """メイン関数。"""
    print("DirectoryScanner デモ")
    print("=" * 50)
    
    # 現在のプロジェクトをスキャン
    demo_current_project()
    
    # 特定のディレクトリをスキャン（存在する場合）
    demo_directories = ["backend", "frontend", "docs", "examples"]
    for dir_name in demo_directories:
        if Path(dir_name).exists():
            demo_specific_directory(dir_name)
    
    print("\n" + "=" * 50)
    print("デモ完了")
    print("\n使用方法:")
    print("  from indexer.file_scanner import DirectoryScanner")
    print("  scanner = DirectoryScanner('/path/to/directory')")
    print("  files = scanner.scan()  # {path: hash} の辞書を返す")


if __name__ == "__main__":
    main()
