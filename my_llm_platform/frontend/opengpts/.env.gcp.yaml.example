OPENAI_API_KEY: your_secret_key_here
LANGCHAIN_TRACING_V2: "true"
LANGCHAIN_PROJECT: langserve-launch-example
LANGCHAIN_API_KEY: your_secret_key_here
FIREWORKS_API_KEY: your_secret_here
AWS_ACCESS_KEY_ID: your_secret_here
AWS_SECRET_ACCESS_KEY: your_secret_here
AZURE_OPENAI_DEPLOYMENT_NAME: your_secret_here
AZURE_OPENAI_API_BASE: your_secret_here
AZURE_OPENAI_API_VERSION: your_secret_here
AZURE_OPENAI_API_KEY: your_secret_here
KAY_API_KEY: your_secret_here
CONNERY_RUNNER_URL: https://your-personal-connery-runner-url
CONNERY_RUNNER_API_KEY: your_secret_here
POSTGRES_HOST: your_postgres_host_here
POSTGRES_PORT: your_postgres_port_here
POSTGRES_DB: your_postgres_db_here
POSTGRES_USER: your_postgres_user_here
POSTGRES_PASSWORD: your_postgres_password_here
