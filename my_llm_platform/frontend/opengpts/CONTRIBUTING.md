# Contributing

## Contributor License Agreement

We are grateful to the contributors who help evolve OpenGPTs and dedicate their time to the project. As the primary sponsor of OpenGPTs, LangChain, Inc. aims to build products in the open that benefit thousands of developers while allowing us to build a sustainable business. For all code contributions to OpenGPTs, we ask that contributors complete and sign a Contributor License Agreement (“CLA”). The agreement between contributors and the project is explicit, so OpenGPTs users can be confident in the legal status of the source code and their right to use it.The CLA does not change the terms of the underlying license, OpenGPTs License, used by our software.

Before you can contribute to OpenGPTs, a bot will comment on the PR asking you to agree to the CLA if you haven't already. Agreeing to the CLA is required before code can be merged and only needs to happen on the first contribution to the project. All subsequent contributions will fall under the same CLA.