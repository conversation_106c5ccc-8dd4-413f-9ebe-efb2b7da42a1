[tool.poetry]
name = "opengpts"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.9.0,<3.12"
sse-starlette = "^1.6.5"
tomli-w = "^1.0.0"
uvicorn = "^0.23.2"
fastapi = "^0.103.2"
# Uncomment if you need to work from a development branch
# This will only work for local development though!
# langchain = { git = "**************:langchain-ai/langchain.git/", branch = "nc/subclass-runnable-binding" , subdirectory = "libs/langchain"}
orjson = "^3.9.10"
python-multipart = "^0.0.6"
tiktoken = "^0"
langchain = "^0.3"
langgraph = "0.2.45"
langgraph-checkpoint-postgres = "^2.0.2"
pydantic = "^2"
langchain-openai = "^0.2"
beautifulsoup4 = "^4.12.3"
boto3 = "^1.34.28"
duckduckgo-search = "^5.3.0"
arxiv = "^2.1.0"
kay = "^0.1.2"
xmltodict = "^0.13.0"
wikipedia = "^1.4.0"
langchain-google-vertexai = "^2.0"
langchain-google-community = "^2.0.1"
setuptools = "^69.0.3"
pdfminer-six = "^20231228"
fireworks-ai = "^0.11.2"
httpx = { version = "^0", extras = ["socks"] }
unstructured = {extras = ["doc", "docx"], version = "^0"}
pgvector = "^0.2.5"
psycopg2-binary = "^2.9.9"
asyncpg = "^0.29.0"
langchain-core = "^0.3"
pyjwt = {extras = ["crypto"], version = "^2.8.0"}
langchain-anthropic = "^0.2"
structlog = "^24.1.0"
python-json-logger = "^2.0.7"

[tool.poetry.group.dev.dependencies]
uvicorn = "^0.23.2"
pygithub = "^2.1.1"

[tool.poetry.group.lint.dependencies]
ruff = "^0.1.4"
codespell = "^2.2.0"

[tool.poetry.group.test.dependencies]
pytest = "^7.2.1"
pytest-cov = "^4.0.0"
pytest-asyncio = "^0.21.1"
pytest-mock = "^3.11.1"
pytest-socket = "^0.6.0"
pytest-watch = "^4.2.0"
pytest-timeout = "^2.2.0"

[tool.coverage.run]
omit = [
    "tests/*",
]

[tool.pytest.ini_options]
# --strict-markers will raise errors on unknown marks.
# https://docs.pytest.org/en/7.1.x/how-to/mark.html#raising-errors-on-unknown-marks
#
# https://docs.pytest.org/en/7.1.x/reference/reference.html
# --strict-config       any warnings encountered while parsing the `pytest`
#                       section of the configuration file raise errors.
addopts = "--strict-markers --strict-config --durations=5 -vv"
# Use global timeout of 30 seconds for now.
# Most tests should be closer to ~100 ms, but some of the tests involve
# parsing files. We can adjust on a per test basis later on.
timeout = 30
asyncio_mode = "auto"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
