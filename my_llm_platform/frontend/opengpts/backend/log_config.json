{"version": 1, "disable_existing_loggers": false, "formatters": {"default": {"()": "uvicorn.logging.DefaultFormatter", "fmt": "%(asctime)s - %(name)s - %(levelprefix)s %(message)s"}, "access": {"()": "uvicorn.logging.AccessFormatter", "fmt": "%(asctime)s - %(name)s - %(levelprefix)s  %(client_addr)s - \"%(request_line)s\" %(status_code)s"}, "json": {"()": "pythonjsonlogger.jsonlogger.JsonFormatter", "fmt": "%(asctime)s - %(name)s - %(levelname)s %(message)s"}}, "handlers": {"default": {"formatter": "default", "class": "logging.StreamHandler", "stream": "ext://sys.stderr"}, "access": {"formatter": "access", "class": "logging.StreamHandler", "stream": "ext://sys.stdout"}, "file": {"formatter": "json", "class": "logging.handlers.RotatingFileHandler", "filename": "./app.log", "mode": "a+", "maxBytes": 10000000, "backupCount": 1}}, "root": {"handlers": ["default", "file"], "level": "INFO"}, "loggers": {"app": {"handlers": ["default", "file"], "level": "INFO", "propagate": false}, "uvicorn": {"handlers": ["default", "file"], "level": "INFO", "propagate": false}, "uvicorn.access": {"handlers": ["access", "file"], "level": "INFO", "propagate": false}}}