{"name": "frontend", "private": true, "version": "0.0.0", "packageManager": "yarn@1.22.19", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "prettier -c src && tsc --noEmit && eslint src --ext ts,tsx --report-unused-disable-directives", "preview": "vite preview", "format": "prettier -w src"}, "dependencies": {"@codemirror/lang-json": "^6.0.1", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@microsoft/fetch-event-source": "^2.0.1", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10", "@uiw/react-codemirror": "^4.21.25", "clsx": "^2.0.0", "dompurify": "^3.0.6", "lodash": "^4.17.21", "marked": "^9.1.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-query": "^3.39.3", "react-router-dom": "^6.22.3", "tailwind-merge": "^2.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/dompurify": "^3.0.4", "@types/lodash": "^4.14.201", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "prettier": "^3.2.5", "tailwindcss": "^3.3.5", "typescript": "^5.0.2", "vite": "^4.4.5"}}